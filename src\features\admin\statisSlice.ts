import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';
import api from "../../config/axiosConfig.tsx";

const URL = '/statistics';

const initialState = {
    subscribersByTrip: { data: [], pagination: null, summary: null },
    subscribersByEstablishment: { data: [], pagination: null, summary: null },
    subscribersByLineKmRange: { data: [], pagination: null, summary: null },
    subscribersByDiscount: { data: [], pagination: null, summary: null },
    revenuesBySalePeriod: { data: [], pagination: null, summary: null },
    subscribersByAgency: { data: [], pagination: null, summary: null },
    subscribersByLine: { data: [], pagination: null, summary: null },
    revenuesByClientGovernorate: { data: [], pagination: null, summary: null },
    revenuesByPaymentMethod: { data: [], pagination: null, summary: null },
    subscriptionsBySubsType: { data: [], pagination: null, summary: null },
    subscribersBySalePoint: { data: [], pagination: null, summary: null },
    loading: false,
    error: null,
    currentItem: null
};

export const getSubscribersByTrip: any = createAsyncThunk(
    "getSubscribersByTrip",
    async (params: {
        subscriptionType?: string;
        startDate?: string;
        endDate?: string;
        salePeriod?: string;
        journey?: string;
        page?: number;
        perPage?: number;
    }, thunkAPI: any) => {
        try {
            let url: string = `${URL}/subscribers-by-trip`;
            const searchParams = [];

            // Add pagination parameters
            if (params.page) {
                searchParams.push(`page=${params.page}`);
            }
            if (params.perPage) {
                searchParams.push(`perPage=${params.perPage}`);
            }

            if (params.subscriptionType && params.subscriptionType !== 'all') {
                searchParams.push(`subscription_type=${params.subscriptionType}`);
            }
            if (params.startDate) {
                searchParams.push(`start_date=${params.startDate}`);
            }
            if (params.endDate) {
                searchParams.push(`end_date=${params.endDate}`);
            }
            if (params.salePeriod && params.salePeriod !== 'all') {
                searchParams.push(`sale_period=${params.salePeriod}`);
            }
            if (params.journey && params.journey !== 'all') {
                searchParams.push(`trip=${params.journey}`);
            }

            if (searchParams.length > 0) {
                url += `?${searchParams.join('&')}`;
            }

            const resp: any = await api.get(url);
            return resp.data;
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = "/unauthorized";
                }
                return thunkAPI.rejectWithValue(data?.message || "An error occurred.");
            } else {
                return thunkAPI.rejectWithValue("An unexpected error occurred.");
            }
        }
    }
);

export const getSubscribersByEstablishment: any = createAsyncThunk(
    "getSubscribersByEstablishment",
    async (params: {
        subscriptionType?: string;
        startDate?: string;
        endDate?: string;
        salePeriod?: string;
        establishment?: string;
        page?: number;
        perPage?: number;
    }, thunkAPI: any) => {
        try {
            let url: string = `${URL}/subscribers-by-establishment`;
            const searchParams = [];

            // Add pagination parameters
            if (params.page) {
                searchParams.push(`page=${params.page}`);
            }
            if (params.perPage) {
                searchParams.push(`perPage=${params.perPage}`);
            }

            if (params.subscriptionType && params.subscriptionType !== 'all') {
                searchParams.push(`subscription_type=${params.subscriptionType}`);
            }
            if (params.startDate) {
                searchParams.push(`start_date=${params.startDate}`);
            }
            if (params.endDate) {
                searchParams.push(`end_date=${params.endDate}`);
            }
            if (params.salePeriod && params.salePeriod !== 'all') {
                searchParams.push(`sale_period=${params.salePeriod}`);
            }
            if (params.establishment && params.establishment !== 'all') {
                searchParams.push(`establishment=${params.establishment}`);
            }

            if (searchParams.length > 0) {
                url += `?${searchParams.join('&')}`;
            }

            const resp: any = await api.get(url);
            return resp.data;
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = "/unauthorized";
                }
                return thunkAPI.rejectWithValue(data?.message || "An error occurred.");
            } else {
                return thunkAPI.rejectWithValue("An unexpected error occurred.");
            }
        }
    }
);

export const getSubscribersByLineKmRange: any = createAsyncThunk(
    "getSubscribersByLineKmRange",
    async (params: {
        subscriptionType?: string;
        startDate?: string;
        endDate?: string;
        salePeriod?: string;
        lineId?: string;
        page?: number;
        perPage?: number;
    }, thunkAPI: any) => {
        try {
            let url: string = `${URL}/subscribers-by-kilometric-range`;
            const searchParams = [];

            // Add pagination parameters
            if (params.page) {
                searchParams.push(`page=${params.page}`);
            }
            if (params.perPage) {
                searchParams.push(`perPage=${params.perPage}`);
            }

            if (params.subscriptionType && params.subscriptionType !== 'all') {
                searchParams.push(`subscriptionType=${params.subscriptionType}`);
            }
            if (params.startDate) {
                searchParams.push(`startDate=${params.startDate}`);
            }
            if (params.endDate) {
                searchParams.push(`endDate=${params.endDate}`);
            }
            if (params.salePeriod && params.salePeriod !== 'all') {
                searchParams.push(`salePeriod=${params.salePeriod}`);
            }
            if (params.lineId && params.lineId !== 'all') {
                searchParams.push(`lineId=${params.lineId}`);
            }

            if (searchParams.length > 0) {
                url += `?${searchParams.join('&')}`;
            }

            const resp: any = await api.get(url);
            return resp.data;
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = "/unauthorized";
                }
                return thunkAPI.rejectWithValue(data?.message || "An error occurred.");
            } else {
                return thunkAPI.rejectWithValue("An unexpected error occurred.");
            }
        }
    }
);

export const getSubscribersByDiscount: any = createAsyncThunk(
    "getSubscribersByDiscount",
    async (params: {
        startDate?: string;
        endDate?: string;
        subscriptionType?: string;
        page?: number;
        perPage?: number;
    }, thunkAPI: any) => {
        try {
            let url: string = `${URL}/subscribers-by-discount`;
            const searchParams = [];

            // Add pagination parameters
            if (params.page) {
                searchParams.push(`page=${params.page}`);
            }
            if (params.perPage) {
                searchParams.push(`perPage=${params.perPage}`);
            }

            if (params.startDate) {
                searchParams.push(`startDate=${params.startDate}`);
            }
            if (params.endDate) {
                searchParams.push(`endDate=${params.endDate}`);
            }
            if (params.subscriptionType && params.subscriptionType !== 'all') {
                searchParams.push(`subscriptionType=${params.subscriptionType}`);
            }

            if (searchParams.length > 0) {
                url += `?${searchParams.join('&')}`;
            }

            const resp: any = await api.get(url);
            return resp.data;
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = "/unauthorized";
                }
                return thunkAPI.rejectWithValue(data?.message || "An error occurred.");
            } else {
                return thunkAPI.rejectWithValue("An unexpected error occurred.");
            }
        }
    }
);

export const getRevenuesBySalePeriod: any = createAsyncThunk(
    "getRevenuesBySalePeriod",
    async (params: {
        subscriptionType?: string;
        startDate?: string;
        endDate?: string;
        page?: number;
        perPage?: number;
    }, thunkAPI: any) => {
        try {
            let url: string = `${URL}/revenues-by-sale-period`;
            const searchParams = [];

            // Add pagination parameters
            if (params.page) {
                searchParams.push(`page=${params.page}`);
            }
            if (params.perPage) {
                searchParams.push(`perPage=${params.perPage}`);
            }

            if (params.subscriptionType && params.subscriptionType !== 'all') {
                searchParams.push(`subscriptionType=${params.subscriptionType}`);
            }
            if (params.startDate) {
                searchParams.push(`startDate=${params.startDate}`);
            }
            if (params.endDate) {
                searchParams.push(`endDate=${params.endDate}`);
            }

            if (searchParams.length > 0) {
                url += `?${searchParams.join('&')}`;
            }

            const resp: any = await api.get(url);
            return resp.data;
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = "/unauthorized";
                }
                return thunkAPI.rejectWithValue(data?.message || "An error occurred.");
            } else {
                return thunkAPI.rejectWithValue("An unexpected error occurred.");
            }
        }
    }
);

export const getSubscribersByAgency: any = createAsyncThunk(
    "getSubscribersByAgency",
    async (params: {
        subscriptionType?: string;
        startDate?: string;
        endDate?: string;
        page?: number;
        perPage?: number;
    }, thunkAPI: any) => {
        try {
            let url: string = `${URL}/subscribers-by-agency`;
            const searchParams = [];

            // Add pagination parameters
            if (params.page) {
                searchParams.push(`page=${params.page}`);
            }
            if (params.perPage) {
                searchParams.push(`perPage=${params.perPage}`);
            }

            if (params.subscriptionType && params.subscriptionType !== 'all') {
                searchParams.push(`subscriptionType=${params.subscriptionType}`);
            }
            if (params.startDate) {
                searchParams.push(`startDate=${params.startDate}`);
            }
            if (params.endDate) {
                searchParams.push(`endDate=${params.endDate}`);
            }

            if (searchParams.length > 0) {
                url += `?${searchParams.join('&')}`;
            }

            const resp: any = await api.get(url);
            return resp.data;
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = "/unauthorized";
                }
                return thunkAPI.rejectWithValue(data?.message || "An error occurred.");
            } else {
                return thunkAPI.rejectWithValue("An unexpected error occurred.");
            }
        }
    }
);

export const getSubscribersByLine: any = createAsyncThunk(
    "getSubscribersByLine",
    async (params: {
        subscriptionType?: string;
        startDate?: string;
        endDate?: string;
        salePeriod?: string;
        lineId?: string;
        page?: number;
        perPage?: number;
    }, thunkAPI: any) => {
        try {
            let url: string = `${URL}/subscribers-by-line`;
            const searchParams = [];

            // Add pagination parameters
            if (params.page) {
                searchParams.push(`page=${params.page}`);
            }
            if (params.perPage) {
                searchParams.push(`perPage=${params.perPage}`);
            }

            if (params.subscriptionType && params.subscriptionType !== 'all') {
                searchParams.push(`subscriptionType=${params.subscriptionType}`);
            }
            if (params.startDate) {
                searchParams.push(`startDate=${params.startDate}`);
            }
            if (params.endDate) {
                searchParams.push(`endDate=${params.endDate}`);
            }
            if (params.salePeriod && params.salePeriod !== 'all') {
                searchParams.push(`salePeriod=${params.salePeriod}`);
            }
            if (params.lineId && params.lineId !== 'all') {
                searchParams.push(`lineId=${params.lineId}`);
            }

            if (searchParams.length > 0) {
                url += `?${searchParams.join('&')}`;
            }

            const resp: any = await api.get(url);
            return resp.data;
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = "/unauthorized";
                }
                return thunkAPI.rejectWithValue(data?.message || "An error occurred.");
            } else {
                return thunkAPI.rejectWithValue("An unexpected error occurred.");
            }
        }
    }
);


export const getRevenuesByClientGovernorate: any = createAsyncThunk(
    "getRevenuesByClientGovernorate",
    async (params: {
        subscriptionType?: string;
        startDate?: string;
        endDate?: string;
        page?: number;
        perPage?: number;
    }, thunkAPI: any) => {
        try {
            let url: string = `${URL}/revenues-by-client-governorate`;
            const searchParams = [];

            // Add pagination parameters
            if (params.page) {
                searchParams.push(`page=${params.page}`);
            }
            if (params.perPage) {
                searchParams.push(`perPage=${params.perPage}`);
            }

            if (params.subscriptionType && params.subscriptionType !== 'all') {
                searchParams.push(`subscriptionType=${params.subscriptionType}`);
            }
            if (params.startDate) {
                searchParams.push(`startDate=${params.startDate}`);
            }
            if (params.endDate) {
                searchParams.push(`endDate=${params.endDate}`);
            }

            if (searchParams.length > 0) {
                url += `?${searchParams.join('&')}`;
            }

            const resp: any = await api.get(url);
            return resp.data;
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = "/unauthorized";
                }
                return thunkAPI.rejectWithValue(data?.message || "An error occurred.");
            } else {
                return thunkAPI.rejectWithValue("An unexpected error occurred.");
            }
        }
    }
);

export const getRevenuesByPaymentMethod: any = createAsyncThunk(
    "getRevenuesByPaymentMethod",
    async (params: {
        subscriptionType?: string;
        startDate?: string;
        endDate?: string;
        page?: number;
        perPage?: number;
    }, thunkAPI: any) => {
        try {
            let url: string = `${URL}/revenues-by-payment-method`;
            const searchParams = [];

            // Add pagination parameters
            if (params.page) {
                searchParams.push(`page=${params.page}`);
            }
            if (params.perPage) {
                searchParams.push(`perPage=${params.perPage}`);
            }

            if (params.subscriptionType) {
                searchParams.push(`subscription_type=${params.subscriptionType}`);
            }
            if (params.startDate) {
                searchParams.push(`start_date=${params.startDate}`);
            }
            if (params.endDate) {
                searchParams.push(`end_date=${params.endDate}`);
            }

            if (searchParams.length > 0) {
                url += `?${searchParams.join('&')}`;
            }

            const resp: any = await api.get(url);
            return resp.data;
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = "/unauthorized";
                }
                return thunkAPI.rejectWithValue(data?.message || "An error occurred.");
            } else {
                return thunkAPI.rejectWithValue("An unexpected error occurred.");
            }
        }
    }
);

export const getSubscriptionsBySubsType: any = createAsyncThunk(
    "getSubscriptionsBySubsType",
    async (params: {
        startDate?: string;
        endDate?: string;
        salePeriod?: string;
        salePoint?: string;
        page?: number;
        perPage?: number;
    }, thunkAPI: any) => {
        try {
            let url: string = `${URL}/subscriptions-by-subs-type`;
            const searchParams = [];

            // Add pagination parameters
            if (params.page) {
                searchParams.push(`page=${params.page}`);
            }
            if (params.perPage) {
                searchParams.push(`perPage=${params.perPage}`);
            }

            if (params.startDate) {
                searchParams.push(`startDate=${params.startDate}`);
            }
            if (params.endDate) {
                searchParams.push(`endDate=${params.endDate}`);
            }
            if (params.salePeriod && params.salePeriod !== 'all') {
                searchParams.push(`salePeriod=${params.salePeriod}`);
            }
            if (params.salePoint && params.salePoint !== 'all') {
                searchParams.push(`salePoint=${params.salePoint}`);
            }

            if (searchParams.length > 0) {
                url += `?${searchParams.join('&')}`;
            }

            const resp: any = await api.get(url);
            return resp.data;
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = "/unauthorized";
                }
                return thunkAPI.rejectWithValue(data?.message || "An error occurred.");
            } else {
                return thunkAPI.rejectWithValue("An unexpected error occurred.");
            }
        }
    }
);

export const getSubscribersBySalePoint: any = createAsyncThunk(
    "getSubscribersBySalePoint",
    async (params: {
        subscriptionType?: string;
        startDate?: string;
        endDate?: string;
        salePeriod?: string;
        salePoint?: string;
        agency?: string;
        page?: number;
        perPage?: number;
    }, thunkAPI: any) => {
        try {
            let url: string = `${URL}/subscribers-by-sale-point`;
            const searchParams = [];

            // Add pagination parameters
            if (params.page) {
                searchParams.push(`page=${params.page}`);
            }
            if (params.perPage) {
                searchParams.push(`perPage=${params.perPage}`);
            }

            if (params.subscriptionType && params.subscriptionType !== 'all') {
                searchParams.push(`subscriptionType=${params.subscriptionType}`);
            }
            if (params.startDate) {
                searchParams.push(`startDate=${params.startDate}`);
            }
            if (params.endDate) {
                searchParams.push(`endDate=${params.endDate}`);
            }
            if (params.salePeriod && params.salePeriod !== 'all') {
                searchParams.push(`salePeriod=${params.salePeriod}`);
            }
            if (params.salePoint && params.salePoint !== 'all') {
                searchParams.push(`salePoint=${params.salePoint}`);
            }
            if (params.agency && params.agency !== 'all') {
                searchParams.push(`agency=${params.agency}`);
            }

            if (searchParams.length > 0) {
                url += `?${searchParams.join('&')}`;
            }

            const resp: any = await api.get(url);
            return resp.data;
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = "/unauthorized";
                }
                return thunkAPI.rejectWithValue(data?.message || "An error occurred.");
            } else {
                return thunkAPI.rejectWithValue("An unexpected error occurred.");
            }
        }
    }
);

const statisSlice = createSlice({
    name: 'statistics',
    initialState,
    reducers: {
        setCurrentItem: (state, action) => {
            state.currentItem = action.payload;
        },
        clearError: (state) => {
            state.error = null;
        },
        clearSubscribersByTrip: (state) => {
            state.subscribersByTrip = { data: [], pagination: null, summary: null };
        },
        clearSubscribersByEstablishment: (state) => {
            state.subscribersByEstablishment = { data: [], pagination: null, summary: null };
        },
        clearSubscribersByLineKmRange: (state) => {
            state.subscribersByLineKmRange = { data: [], pagination: null, summary: null };
        },
        clearSubscribersByDiscount: (state) => {
            state.subscribersByDiscount = { data: [], pagination: null, summary: null };
        },
        clearRevenuesBySalePeriod: (state) => {
            state.revenuesBySalePeriod = { data: [], pagination: null, summary: null };
        },
        clearSubscribersByAgency: (state) => {
            state.subscribersByAgency = { data: [], pagination: null, summary: null };
        },
        clearSubscribersByLine: (state) => {
            state.subscribersByLine = { data: [], pagination: null, summary: null };
        },
        clearRevenuesByClientGovernorate: (state) => {
            state.revenuesByClientGovernorate = { data: [], pagination: null, summary: null };
        },
        clearRevenuesByPaymentMethod: (state) => {
            state.revenuesByPaymentMethod = { data: [], pagination: null, summary: null };
        },
        clearSubscriptionsBySubsType: (state) => {
            state.subscriptionsBySubsType = { data: [], pagination: null, summary: null };
        },
        clearSubscribersBySalePoint: (state) => {
            state.subscribersBySalePoint = { data: [], pagination: null, summary: null };
        }
    },
    extraReducers: (builder) => {
        builder
            // getSubscribersByTrip
            .addCase(getSubscribersByTrip.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(getSubscribersByTrip.fulfilled, (state, action) => {
                state.loading = false;
                state.subscribersByTrip = action.payload;
            })
            .addCase(getSubscribersByTrip.rejected, (state: any, action) => {
                state.loading = false;
                state.error = action.payload as string;
            })
            // getSubscribersByEstablishment
            .addCase(getSubscribersByEstablishment.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(getSubscribersByEstablishment.fulfilled, (state, action) => {
                state.loading = false;
                state.subscribersByEstablishment = action.payload;
            })
            .addCase(getSubscribersByEstablishment.rejected, (state: any, action) => {
                state.loading = false;
                state.error = action.payload as string;
            })
            // getSubscribersByLineKmRange
            .addCase(getSubscribersByLineKmRange.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(getSubscribersByLineKmRange.fulfilled, (state, action) => {
                state.loading = false;
                state.subscribersByLineKmRange = action.payload;
            })
            .addCase(getSubscribersByLineKmRange.rejected, (state: any, action) => {
                state.loading = false;
                state.error = action.payload as string;
            })
            // getSubscribersByDiscount
            .addCase(getSubscribersByDiscount.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(getSubscribersByDiscount.fulfilled, (state, action) => {
                state.loading = false;
                state.subscribersByDiscount = action.payload;
            })
            .addCase(getSubscribersByDiscount.rejected, (state: any, action) => {
                state.loading = false;
                state.error = action.payload as string;
            })
            // getRevenuesBySalePeriod
            .addCase(getRevenuesBySalePeriod.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(getRevenuesBySalePeriod.fulfilled, (state, action) => {
                state.loading = false;
                state.revenuesBySalePeriod = action.payload;
            })
            .addCase(getRevenuesBySalePeriod.rejected, (state: any, action) => {
                state.loading = false;
                state.error = action.payload as string;
            })
            // getSubscribersByAgency
            .addCase(getSubscribersByAgency.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(getSubscribersByAgency.fulfilled, (state, action) => {
                state.loading = false;
                state.subscribersByAgency = action.payload;
            })
            .addCase(getSubscribersByAgency.rejected, (state: any, action) => {
                state.loading = false;
                state.error = action.payload as string;
            })
            // getSubscribersByLine
            .addCase(getSubscribersByLine.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(getSubscribersByLine.fulfilled, (state, action) => {
                state.loading = false;
                state.subscribersByLine = action.payload;
            })
            .addCase(getSubscribersByLine.rejected, (state: any, action) => {
                state.loading = false;
                state.error = action.payload as string;
            })
            // getRevenuesByClientGovernorate
            .addCase(getRevenuesByClientGovernorate.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(getRevenuesByClientGovernorate.fulfilled, (state, action) => {
                state.loading = false;
                state.revenuesByClientGovernorate = action.payload;
            })
            .addCase(getRevenuesByClientGovernorate.rejected, (state: any, action) => {
                state.loading = false;
                state.error = action.payload as string;
            })
            // getRevenuesByPaymentMethod
            .addCase(getRevenuesByPaymentMethod.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(getRevenuesByPaymentMethod.fulfilled, (state, action) => {
                state.loading = false;
                state.revenuesByPaymentMethod = action.payload;
            })
            .addCase(getRevenuesByPaymentMethod.rejected, (state: any, action) => {
                state.loading = false;
                state.error = action.payload as string;
            })
            // getSubscriptionsBySubsType
            .addCase(getSubscriptionsBySubsType.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(getSubscriptionsBySubsType.fulfilled, (state, action) => {
                state.loading = false;
                state.subscriptionsBySubsType = action.payload;
            })
            .addCase(getSubscriptionsBySubsType.rejected, (state: any, action) => {
                state.loading = false;
                state.error = action.payload as string;
            })
            // getSubscribersBySalePoint
            .addCase(getSubscribersBySalePoint.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(getSubscribersBySalePoint.fulfilled, (state, action) => {
                state.loading = false;
                state.subscribersBySalePoint = action.payload;
            })
            .addCase(getSubscribersBySalePoint.rejected, (state: any, action) => {
                state.loading = false;
                state.error = action.payload as string;
            })
    }
});

export const {
    setCurrentItem,
    clearError,
    clearSubscribersByTrip,
    clearSubscribersByEstablishment,
    clearSubscribersByLineKmRange,
    clearSubscribersByDiscount,
    clearRevenuesBySalePeriod,
    clearSubscribersByAgency,
    clearSubscribersByLine,
    clearRevenuesByClientGovernorate,
    clearRevenuesByPaymentMethod,
    clearSubscriptionsBySubsType,
    clearSubscribersBySalePoint
} = statisSlice.actions;

export default statisSlice.reducer;
