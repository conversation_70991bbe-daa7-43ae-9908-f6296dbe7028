import React, { useState, useEffect, useRef } from "react";
import {
  Select,
  Form,
  DatePicker,
  Button,
  Input,
  Breadcrumb,
  Al<PERSON>,
  Row,
  Col,
  Typography,
  Collapse,
} from "antd";
import {
  SettingOutlined,
  Bar<PERSON><PERSON>Outlined,
  Line<PERSON><PERSON>Outlined,
  <PERSON><PERSON><PERSON>Outlined,
  FilterOutlined,
  UserOutlined,
  DollarOutlined,
  DatabaseOutlined,
  TrophyOutlined
} from "@ant-design/icons";
import { Column, Pie, Line } from "@ant-design/charts";
import ProTable from "@ant-design/pro-table";
import { Link } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { useDispatch, useSelector } from "react-redux";
import { getSubscribersByTrip, clearSubscribersByTrip, getSubscribersByEstablishment, clearSubscribersByEstablishment, getSubscribersByLineKmRange, clearSubscribersByLineKmRange, getSubscribersByDiscount, clearSubscribersByDiscount, getRevenuesBySalePeriod, clearRevenuesBySalePeriod, getSubscribersByAgency, clearSubscribersByAgency, getSubscribersByLine, clearSubscribersByLine, getRevenuesByClientGovernorate, clearRevenuesByClientGovernorate, getRevenuesByPaymentMethod, clearRevenuesByPaymentMethod, getSubscriptionsBySubsType, clearSubscriptionsBySubsType, getSubscribersBySalePoint, clearSubscribersBySalePoint } from "../../../../features/admin/statisSlice";
import { getAbnTypesAll } from "../../../../features/admin/abnTypeSlice";
import { getTripsAll, getTripsNotInterAll } from "../../../../features/admin/tripsSlice";
import { getSalesPeriodsAll } from "../../../../features/admin/salesPeriodsSlice";
import { getEstablishmentAll } from "../../../../features/admin/establishmentSlice";
import { getLinesAll } from "../../../../features/admin/lineSlice";
import { getDiscountsAll } from "../../../../features/admin/discountSlice";
import { getAgencyAll } from "../../../../features/admin/agencySlice";
import { getSalePointAll } from "../../../../features/admin/salePointSlice";
import dayjs from "dayjs";
import { assets } from "../../../../assets/assets";

const { Option } = Select;
const { Title } = Typography;
const { Panel } = Collapse;

const ManageStats: React.FC = () => {
  const [form] = Form.useForm();
  const { t, i18n } = useTranslation();
  const currentLang = i18n.language;

  // Obtenir le nom du mois précédent
  const getLastMonthName = () => {
    const lastMonth = dayjs().subtract(1, 'month');
    if (currentLang === 'fr') {
      return lastMonth.locale('fr').format('MMMM YYYY');
    } else if (currentLang === 'en') {
      return lastMonth.locale('en').format('MMMM YYYY');
    } else if (currentLang === 'ar') {
      return lastMonth.locale('ar').format('MMMM YYYY');
    } else {
      return lastMonth.format('MMMM YYYY');
    }
  };
  const dispatch = useDispatch();
  const [selectedStatistic, setSelectedStatistic] = useState<string>("1");
  const [chartType, setChartType] = useState<'column' | 'pie' | 'line'>('pie');

  // Pagination state
  const [pageNumber, setPageNumber] = useState<number>(1);
  const [pageSize, setPageSize] = useState<number>(15);
  const [total, setTotal] = useState(0);
  const [tableLoading, setTableLoading] = useState(false);
  const actionRef = useRef<any>();

  const { subscribersByTrip, subscribersByEstablishment, subscribersByLineKmRange, subscribersByDiscount, revenuesBySalePeriod, subscribersByAgency, subscribersByLine, revenuesByClientGovernorate, revenuesByPaymentMethod, subscriptionsBySubsType, subscribersBySalePoint, loading, error } = useSelector((state: any) => state.statis);
  const abnTypes  = useSelector((state: any) => state.abnType.items.data);
  const trips  = useSelector((state: any) => state.trips.items.data);
  const salesPeriods  = useSelector((state: any) => state.salesPeriod.items.data);
  const establishments = useSelector((state: any) => state.establishment.items.data);
  const lines = useSelector((state: any) => state.line.items.data);
  const discounts = useSelector((state: any) => state.discount.items.data);
  const agencies = useSelector((state: any) => state.agency.items.data);
  const salePoints = useSelector((state: any) => state.salePoint.items.data);


  const fetchStoreData = async () => {
            if(!abnTypes?.length){
                await dispatch(getAbnTypesAll()).unwrap()
            }
            if(!trips?.length){
                await dispatch(getTripsNotInterAll()).unwrap()
            }
            if(!salesPeriods?.length){
                await dispatch(getSalesPeriodsAll()).unwrap()
            }
            if(!establishments?.length){
                await dispatch(getEstablishmentAll()).unwrap()
            }
             if(!lines?.length){
                await dispatch(getLinesAll()).unwrap()
            }
             if(!discounts?.length){
                await dispatch(getDiscountsAll()).unwrap()
            }
             if(!agencies?.length){
                await dispatch(getAgencyAll()).unwrap()
            }
             if(!salePoints?.length){
                await dispatch(getSalePointAll()).unwrap()
            }
        }



  useEffect(() => {
    fetchStoreData()
    dispatch(getSubscribersByTrip({}));
  }, [dispatch]);

  const calculateSummaryStats = () => {
    if (selectedStatistic === "3") {
      return calculateLineKmSummaryStats();
    }
    if (selectedStatistic === "4") {
      return calculateDiscountSummaryStats();
    }
    if (selectedStatistic === "5") {
      return calculateSalePeriodSummaryStats();
    }
    if (selectedStatistic === "6") {
      return calculateAgencySummaryStats();
    }
    if (selectedStatistic === "7") {
      return calculateLineSummaryStats();
    }
    if (selectedStatistic === "8") {
      return calculateGovernorateRevenueSummaryStats();
    }
    if (selectedStatistic === "9") {
      return calculatePaymentMethodRevenueSummaryStats();
    }
    if (selectedStatistic === "10") {
      return calculateSubscriptionsBySubsTypeSummaryStats();
    }
    if (selectedStatistic === "11") {
      return calculateSalePointSummaryStats();
    }

    // Pour les statistiques 1 et 2, utiliser les données summary.all_times
    if (selectedStatistic === "1" && subscribersByTrip?.summary) {
      const summary = subscribersByTrip.summary.all_times;
      return {
        totalSubscribers: summary.total_subscribers || 0,
        totalTransactions: summary.total_transactions || 0,
        totalAmount: summary.total_amount || 0,
        totalItems: summary.total_trips || 0,
        averagePerItem: summary.total_trips > 0 ? (summary.total_amount || 0) / summary.total_trips : 0
      };
    }

    if (selectedStatistic === "2" && subscribersByEstablishment?.summary) {
      const summary = subscribersByEstablishment.summary.all_times;
      return {
        totalSubscribers: summary.total_subscribers || 0,
        totalTransactions: summary.total_transactions || 0,
        totalAmount: summary.total_amount || 0,
        totalItems: summary.total_establishments || 0,
        averagePerItem: summary.total_establishments > 0 ? (summary.total_amount || 0) / summary.total_establishments : 0
      };
    }

    return {
      totalSubscribers: 0,
      totalTransactions: 0,
      totalAmount: 0,
      totalItems: 0,
      averagePerItem: 0
    };
  };

  const prepareChartData = () => {
    if (selectedStatistic === "3") {
      return prepareLineKmChartData();
    }
    if (selectedStatistic === "4") {
      return prepareDiscountChartData();
    }
    if (selectedStatistic === "5") {
      return prepareSalePeriodChartData();
    }
    if (selectedStatistic === "6") {
      return prepareAgencyChartData();
    }
    if (selectedStatistic === "7") {
      return prepareLineChartData();
    }
    if (selectedStatistic === "8") {
      return prepareGovernorateRevenueChartData();
    }
    if (selectedStatistic === "9") {
      return preparePaymentMethodRevenueChartData();
    }
    if (selectedStatistic === "10") {
      return prepareSubscriptionsBySubsTypeChartData();
    }
    if (selectedStatistic === "11") {
      return prepareSalePointChartData();
    }

    let currentData;
    if (selectedStatistic === "1") {
      currentData = subscribersByTrip?.data;
    } else if (selectedStatistic === "2") {
      currentData = subscribersByEstablishment?.data;
    }

    if (!currentData?.length) return [];

    if (selectedStatistic === "1") {
      return currentData.map((item: any) => ({
        nom: item[`nom_${currentLang}`] || item.trip_name || `Trajet ${item.trip_id}`,
        abonnes: item.last_month?.subscriber_count || 0,
        transactions: item.last_month?.transaction_count || 0,
        montant: item.last_month?.total_amount || 0,
      }));
    } else if (selectedStatistic === "2") {
      return currentData.map((item: any) => ({
        nom: item[`nom_${currentLang}`] || item.establishment_name || `Établissement ${item.establishment_id}`,
        abonnes: item.last_month?.subscriber_count || 0,
        transactions: item.last_month?.transaction_count || 0,
        montant: item.last_month?.total_amount || 0,
      }));
    }

    return [];
  };

  const getSubscriptionTypeOptions = () => {
    const options = [{ value: "all", label: t("common.all") }];
    abnTypes?.forEach((type: any) => {
      options.push({
        value: type.id.toString(),
        label: type.nom_fr || type.nom_en || type.nom_ar
      });
    });
    return options;
  };

  const getTripOptions = () => {
    const options = [{ value: "all", label: t("common.all") }];
    trips?.forEach((trip: any) => {
      options.push({
        value: trip.id.toString(),
        label: trip.nom_fr || trip.nom_en || trip.nom_ar
      });
    });
    return options;
  };

  const getSalePeriodOptions = () => {
    const options = [{ value: "all", label: t("common.all") }];
    salesPeriods?.forEach((period: any) => {
      options.push({
        value: period.id.toString(),
        label: period.nom_fr || period.nom_en || period.nom_ar
      });
    });
    return options;
  };

  const getEstablishmentOptions = () => {
    const options = [{ value: "all", label: t("common.all") }];
    establishments?.forEach((establishment: any) => {
      options.push({
        value: establishment.id.toString(),
        label: establishment.nom_fr || establishment.nom_en || establishment.nom_ar
      });
    });
    return options;
  };

  const getLineOptions = () => {
    const options = [{ value: "all", label: t("common.all") }];
    lines?.forEach((line: any) => {
      options.push({
        value: line.id.toString(),
        label: line.nom_fr || line.nom_en || line.nom_ar
      });
    });
    return options;
  };

  const getSalePointOptions = () => {
    const options = [{ value: "all", label: t("common.all") }];
    salePoints?.forEach((salePoint: any) => {
      options.push({
        value: salePoint.id.toString(),
        label: salePoint.nom_fr || salePoint.nom_en || salePoint.nom_ar
      });
    });
    return options;
  };

  const getAgencyOptions = () => {
    const options = [{ value: "all", label: t("common.all") }];
    agencies?.forEach((agency: any) => {
      options.push({
        value: agency.id.toString(),
        label: agency.nom_fr || agency.nom_en || agency.nom_ar
      });
    });
    return options;
  };

  // Request handlers for ProTable
  const handleGetStatisticsData = async (params: any, sort: any, filter: any) => {
    const { current, pageSize, ...otherParams } = params;

    // Convert form values to API parameters
    const apiParams = {
      page: current || pageNumber,
      perPage: pageSize || 15,
      ...otherParams
    };

    try {
      let result;
      switch (selectedStatistic) {
        case "1":
          result = await dispatch(getSubscribersByTrip(apiParams)).unwrap();
          break;
        case "2":
          result = await dispatch(getSubscribersByEstablishment(apiParams)).unwrap();
          break;
        case "3":
          result = await dispatch(getSubscribersByLineKmRange(apiParams)).unwrap();
          break;
        case "4":
          result = await dispatch(getSubscribersByDiscount(apiParams)).unwrap();
          break;
        case "5":
          result = await dispatch(getRevenuesBySalePeriod(apiParams)).unwrap();
          break;
        case "6":
          result = await dispatch(getSubscribersByAgency(apiParams)).unwrap();
          break;
        case "7":
          result = await dispatch(getSubscribersByLine(apiParams)).unwrap();
          break;
        case "8":
          result = await dispatch(getRevenuesByClientGovernorate(apiParams)).unwrap();
          break;
        case "9":
          result = await dispatch(getRevenuesByPaymentMethod(apiParams)).unwrap();
          break;
        case "10":
          result = await dispatch(getSubscriptionsBySubsType(apiParams)).unwrap();
          break;
        case "11":
          result = await dispatch(getSubscribersBySalePoint(apiParams)).unwrap();
          break;
        default:
          result = { data: [], meta: { total: 0 } };
      }

      if (result?.meta?.total) {
        setTotal(result.meta.total);
      }

      // Handle special case for kilometric range data flattening
      if (selectedStatistic === "3") {
        return flattenLineKmData();
      }

      return result.data || [];
    } catch (error) {
      console.error('Error fetching statistics data:', error);
      return [];
    }
  };

  const STATISTICS_CONFIG: Record<string, any> = {
    "1": {
      id: "1",
      label: "manage_stats.abonnees_trajet",
      filters: [
        {
          name: "subscriptionType",
          label: "manage_stats.type_abn",
          type: "select",
          options: getSubscriptionTypeOptions(),
        },
        {
          name: "startDate",
          label: "manage_stats.start_date",
          type: "date",
        },
        {
          name: "endDate",
          label: "manage_stats.end_date",
          type: "date",
        },
        {
          name: "salePeriod",
          label: "manage_stats.period",
          type: "select",
          options: getSalePeriodOptions(),
        },
        {
          name: "journey",
          label: "manage_stats.trajet",
          type: "select",
          options: getTripOptions(),
        },
      ],
      columns: [
        {
          title: t("manage_stats.trajet"),
          dataIndex: `nom_${currentLang}`,
          key: "trip_name",
          render: (_: string, record: any) => record?.[`nom_${currentLang}`] || '-',
        },
        {
          title: t("manage_stats.nb_abonnees"),
          dataIndex: ["all_times", "subscriber_count"],
          key: "subscriber_count_total",
          render: (_: string, record: any) => record?.all_times?.subscriber_count || 0,
        },
        {
          title: t("manage_stats.nb_transactions"),
          dataIndex: ["all_times", "transaction_count"],
          key: "transaction_count_total",
          render: (_: string, record: any) => record?.all_times?.transaction_count || 0,
        },
        {
          title: t("manage_stats.montant"),
          dataIndex: ["all_times", "total_amount"],
          key: "total_amount_all",
          render: (_: string, record: any) => `${record?.all_times?.total_amount?.toFixed(2) || 0} DT`
        },
      ],
    },

    "2": {
      id: "2",
      label: "manage_stats.sub_etab",
      filters: [
        {
          name: "subscriptionType",
          label: "manage_stats.type_abn",
          type: "select",
          options: getSubscriptionTypeOptions(),
        },
        {
          name: "startDate",
          label: "manage_stats.start_date",
          type: "date",
        },
        {
          name: "endDate",
          label: "manage_stats.end_date",
          type: "date",
        },
        {
          name: "salePeriod",
          label: "manage_stats.period",
          type: "select",
          options: getSalePeriodOptions(),
        },
        {
          name: "establishment",
          label: "manage_stats.etab",
          type: "select",
          options: getEstablishmentOptions(),
        },
      ],
      columns: [
        {
          title: t("manage_stats.etab"),
          dataIndex: `nom_${currentLang}`,
          key: "establishment_name",
          render: (_: string, record: any) => record?.[`nom_${currentLang}`] || record?.establishment_name || '-',
        },
        {
          title: t("manage_stats.nb_abonnees"),
          dataIndex: ["all_times", "subscriber_count"],
          key: "subscriber_count_total",
          render: (_: string, record: any) => record?.all_times?.subscriber_count || 0,
        },
        {
          title: t("manage_stats.nb_transactions"),
          dataIndex: ["all_times", "transaction_count"],
          key: "transaction_count_total",
          render: (_: string, record: any) => record?.all_times?.transaction_count || 0,
        },
        {
          title: t("manage_stats.montant"),
          dataIndex: ["all_times", "total_amount"],
          key: "total_amount_all",
          render: (_: string, record: any) => `${record?.all_times?.total_amount?.toFixed(2) || 0} DT`
        },
      ],
    },

    "3": {
      id: "3",
      label: "manage_stats.sub_trancheKm_ligne",
      filters: [
        {
          name: "subscriptionType",
          label: "manage_stats.type_abn",
          type: "select",
          options: getSubscriptionTypeOptions(),
        },
        {
          name: "startDate",
          label: "manage_stats.start_date",
          type: "date",
        },
        {
          name: "endDate",
          label: "manage_stats.end_date",
          type: "date",
        },
        {
          name: "salePeriod",
          label: "manage_stats.period",
          type: "select",
          options: getSalePeriodOptions(),
        },
        {
          name: "lineId",
          label: "manage_stats.ligne",
          type: "select",
          options: getLineOptions(),
        },
      ],
      columns: [
        {
          title: t("manage_stats.tranch_km"),
          dataIndex: "kilometric_range",
          key: "kilometric_range",
        },
        {
          title: t("manage_stats.nb_abonnees"),
          dataIndex: ["all_times", "subscriber_count"],
          key: "subscriber_count_total",
          render: (_: string, record: any) => record?.all_times?.subscriber_count || 0,
        },
        {
          title: t("manage_stats.nb_transactions"),
          dataIndex: ["all_times", "transaction_count"],
          key: "transaction_count_total",
          render: (_: string, record: any) => record?.all_times?.transaction_count || 0,
        },
        {
          title: t("manage_stats.montant"),
          dataIndex: ["all_times", "total_amount"],
          key: "total_amount_all",
          render: (_: string, record: any) => `${record?.all_times?.total_amount?.toFixed(2) || 0} DT`
        },
      ],
    },

    "4": {
      id: "4",
      label: "manage_stats.abonnees_remise",
      filters: [
        {
          name: "subscriptionType",
          label: "manage_stats.type_abn",
          type: "select",
          options: getSubscriptionTypeOptions(),
        },
        {
          name: "startDate",
          label: "manage_stats.start_date",
          type: "date",
        },
        {
          name: "endDate",
          label: "manage_stats.end_date",
          type: "date",
        },
      ],
      columns: [
        {
          title: t("manage_stats.remise"),
          dataIndex: `nom_${currentLang}`,
          key: "discount_name",
          render: (_: string, record: any) => {
            const name = record?.[`nom_${currentLang}`] || record?.nom_fr || '-';
            const percentage = record?.percentage ? ` (${record.percentage}%)` : '';
            return `${name}${percentage}`;
          },
        },
        {
          title: t("manage_stats.nb_abonnements"),
          dataIndex: ["all_times", "nombre_abonnements"],
          key: "nombre_abonnements_total",
          render: (_: string, record: any) => record?.all_times?.nombre_abonnements || 0,
        },
        {
          title: t("manage_stats.nb_transactions"),
          dataIndex: ["all_times", "nombre_transactions"],
          key: "nombre_transactions_total",
          render: (_: string, record: any) => record?.all_times?.nombre_transactions || 0,
        },
        {
          title: t("manage_stats.montant"),
          dataIndex: ["all_times", "montant_total"],
          key: "montant_total_all",
          render: (_: string, record: any) => `${record?.all_times?.montant_total?.toFixed(2) || 0} DT`
        },
      ],
    },

    "5": {
      id: "5",
      label: "manage_stats.recettes_periode_vente",
      filters: [
        {
          name: "subscriptionType",
          label: "manage_stats.type_abn",
          type: "select",
          options: getSubscriptionTypeOptions(),
        },
        {
          name: "startDate",
          label: "manage_stats.start_date",
          type: "date",
        },
        {
          name: "endDate",
          label: "manage_stats.end_date",
          type: "date",
        },
      ],
      columns: [
        {
          title: t("manage_stats.campagne"),
          dataIndex: `campaign_nom_${currentLang}`,
          key: "campaign_name",
          render: (_: string, record: any) => record?.[`campaign_nom_${currentLang}`] || record?.campaign_nom_fr || '-',
        },
        {
          title: t("manage_stats.periode_vente"),
          dataIndex: `sale_period_nom_${currentLang}`,
          key: "sale_period_name",
          render: (_: string, record: any) => {
            const name = record?.[`sale_period_nom_${currentLang}`] || record?.sale_period_nom_fr || '-';
            const dateStart = record?.date_start ? dayjs(record.date_start).format('DD/MM/YYYY') : '';
            const dateEnd = record?.date_end ? dayjs(record.date_end).format('DD/MM/YYYY') : '';
            const dates = dateStart && dateEnd ? ` (${dateStart} - ${dateEnd})` : '';
            return `${name}${dates}`;
          },
        },
        {
          title: t("manage_stats.nb_transactions"),
          dataIndex: ["all_times", "nombre_transactions"],
          key: "nombre_transactions_total",
          render: (_: string, record: any) => record?.all_times?.nombre_transactions || 0,
        },
        {
          title: t("manage_stats.nb_abonnements"),
          dataIndex: ["all_times", "nombre_abonnements"],
          key: "nombre_abonnements_total",
          render: (_: string, record: any) => record?.all_times?.nombre_abonnements || 0,
        },
        {
          title: t("manage_stats.montant"),
          dataIndex: ["all_times", "montant_total"],
          key: "montant_total_all",
          render: (_: string, record: any) => `${record?.all_times?.montant_total?.toFixed(2) || 0} DT`
        },
      ],
    },

    "6": {
      id: "6",
      label: "manage_stats.recettes_agence",
      filters: [
        {
          name: "subscriptionType",
          label: "manage_stats.type_abn",
          type: "select",
          options: getSubscriptionTypeOptions(),
        },
        {
          name: "startDate",
          label: "manage_stats.start_date",
          type: "date",
        },
        {
          name: "endDate",
          label: "manage_stats.end_date",
          type: "date",
        },
      ],
      columns: [
        {
          title: t("manage_stats.agence"),
          dataIndex: `nom_${currentLang}`,
          key: "agency_name",
          render: (_: string, record: any) => record?.[`nom_${currentLang}`] || record?.agency_name || '-',
        },
        {
          title: t("manage_stats.nb_abonnees"),
          dataIndex: ["all_times", "subscriber_count"],
          key: "subscriber_count_total",
          render: (_: string, record: any) => record?.all_times?.subscriber_count || 0,
        },
        {
          title: t("manage_stats.nb_transactions"),
          dataIndex: ["all_times", "transaction_count"],
          key: "transaction_count_total",
          render: (_: string, record: any) => record?.all_times?.transaction_count || 0,
        },
        {
          title: t("manage_stats.montant"),
          dataIndex: ["all_times", "total_amount"],
          key: "total_amount_all",
          render: (_: string, record: any) => `${record?.all_times?.total_amount?.toFixed(2) || 0} DT`
        },
      ],
    },

    "7": {
      id: "7",
      label: "manage_stats.abonnees_ligne",
      filters: [
        {
          name: "subscriptionType",
          label: "manage_stats.type_abn",
          type: "select",
          options: getSubscriptionTypeOptions(),
        },
        {
          name: "startDate",
          label: "manage_stats.start_date",
          type: "date",
        },
        {
          name: "endDate",
          label: "manage_stats.end_date",
          type: "date",
        },
        {
          name: "salePeriod",
          label: "manage_stats.period",
          type: "select",
          options: getSalePeriodOptions(),
        },
        {
          name: "lineId",
          label: "manage_stats.ligne",
          type: "select",
          options: getLineOptions(),
        },
      ],
      columns: [
        {
          title: t("manage_stats.ligne"),
          dataIndex: `nom_${currentLang}`,
          key: "line_name",
          render: (_: string, record: any) => record?.[`nom_${currentLang}`] || record?.line_name_fr || record?.code || '-',
        },
        {
          title: t("manage_stats.nb_abonnees"),
          dataIndex: ["all_times", "subscriber_count"],
          key: "subscriber_count_total",
          render: (_: string, record: any) => record?.all_times?.subscriber_count || 0,
        },
        {
          title: t("manage_stats.nb_transactions"),
          dataIndex: ["all_times", "transaction_count"],
          key: "transaction_count_total",
          render: (_: string, record: any) => record?.all_times?.transaction_count || 0,
        },
        {
          title: t("manage_stats.montant"),
          dataIndex: ["all_times", "total_amount"],
          key: "total_amount_all",
          render: (_: string, record: any) => `${record?.all_times?.total_amount?.toFixed(2) || 0} DT`
        },
      ],
    },

    "8": {
      id: "8",
      label: "manage_stats.recettes_gouvernorat",
      filters: [
        {
          name: "subscriptionType",
          label: "manage_stats.type_abn",
          type: "select",
          options: getSubscriptionTypeOptions(),
        },
        {
          name: "startDate",
          label: "manage_stats.start_date",
          type: "date",
        },
        {
          name: "endDate",
          label: "manage_stats.end_date",
          type: "date",
        },
      ],
      columns: [
        {
          title: t("manage_stats.gouvernorat"),
          dataIndex: `governorate_nom_${currentLang}`,
          key: "governorate_name",
          render: (_: string, record: any) => record?.[`governorate_nom_${currentLang}`] || record?.governorate_nom_fr || '-',
        },
        {
          title: t("manage_stats.nb_abonnements"),
          dataIndex: ["all_times", "nombre_abonnements"],
          key: "nombre_abonnements_total",
          render: (_: string, record: any) => record?.all_times?.nombre_abonnements || 0,
        },
        {
          title: t("manage_stats.nb_transactions"),
          dataIndex: ["all_times", "nombre_transactions"],
          key: "nombre_transactions_total",
          render: (_: string, record: any) => record?.all_times?.nombre_transactions || 0,
        },
        {
          title: t("manage_stats.montant"),
          dataIndex: ["all_times", "montant_total"],
          key: "montant_total_all",
          render: (_: string, record: any) => `${record?.all_times?.montant_total?.toFixed(2) || 0} DT`
        },
      ],
    },

    "9": {
      id: "9",
      label: "manage_stats.recettes_methode_paiement",
      filters: [
        {
          name: "subscriptionType",
          label: "manage_stats.type_abn",
          type: "select",
          options: getSubscriptionTypeOptions(),
        },
        {
          name: "startDate",
          label: "manage_stats.start_date",
          type: "date",
        },
        {
          name: "endDate",
          label: "manage_stats.end_date",
          type: "date",
        },
      ],
      columns: [
        {
          title: t("manage_stats.methode_paiement"),
          dataIndex: `payment_method_nom_${currentLang}`,
          key: "payment_method",
          render: (_: string, record: any) => record?.[`payment_method_nom_${currentLang}`] || record?.payment_method_nom_fr || '-',
        },
        {
          title: t("manage_stats.nb_abonnements"),
          dataIndex: ["all_times", "nombre_abonnements"],
          key: "nombre_abonnements_total",
          render: (_: string, record: any) => record?.all_times?.nombre_abonnements || 0,
        },
        {
          title: t("manage_stats.nb_transactions"),
          dataIndex: ["all_times", "nombre_transactions"],
          key: "nombre_transactions_total",
          render: (_: string, record: any) => record?.all_times?.nombre_transactions || 0,
        },
        {
          title: t("manage_stats.montant"),
          dataIndex: ["all_times", "montant_total"],
          key: "montant_total_all",
          render: (_: string, record: any) => `${record?.all_times?.montant_total?.toFixed(2) || 0} DT`
        },
        {
          title: t("manage_stats.frais_carte"),
          dataIndex: ["all_times", "montant_frais_carte"],
          key: "montant_frais_carte_all",
          render: (_: string, record: any) => `${record?.all_times?.montant_frais_carte?.toFixed(2) || 0} DT`
        },
      ],
    },

    "10": {
      id: "10",
      label: "manage_stats.abonnements_type_abn",
      filters: [
        {
          name: "startDate",
          label: "manage_stats.start_date",
          type: "date",
        },
        {
          name: "endDate",
          label: "manage_stats.end_date",
          type: "date",
        },
        {
          name: "salePeriod",
          label: "manage_stats.period",
          type: "select",
          options: getSalePeriodOptions(),
        },
        {
          name: "salePoint",
          label: "manage_stats.point_vente",
          type: "select",
          options: getSalePointOptions(),
        },
      ],
      columns: [
        {
          title: t("manage_stats.type_abn"),
          dataIndex: `subs_type_nom_${currentLang}`,
          key: "subs_type_name",
          render: (_: string, record: any) => {
            const name = record?.[`subs_type_nom_${currentLang}`] || record?.subs_type_nom_fr || '-';
            return record?.is_impersonal ? record?.type_display_name || name : name;
          },
        },
        {
          title: t("manage_stats.nb_transactions"),
          dataIndex: ["all_times", "nombre_transactions"],
          key: "nombre_transactions_total",
          render: (_: string, record: any) => record?.all_times?.nombre_transactions || 0,
        },
        {
          title: t("manage_stats.nb_abonnements"),
          dataIndex: ["all_times", "nombre_abonnements"],
          key: "nombre_abonnements_total",
          render: (_: string, record: any) => record?.all_times?.nombre_abonnements || 0,
        },
        {
          title: t("manage_stats.montant"),
          dataIndex: ["all_times", "montant_total"],
          key: "montant_total_all",
          render: (_: string, record: any) => `${record?.all_times?.montant_total?.toFixed(2) || 0} DT`
        },
        {
          title: t("manage_stats.frais_carte"),
          dataIndex: ["all_times", "montant_frais_carte"],
          key: "montant_frais_carte_all",
          render: (_: string, record: any) => `${record?.all_times?.montant_frais_carte?.toFixed(2) || 0} DT`
        },
      ],
    },

    "11": {
      id: "11",
      label: "manage_stats.point_vente",
      filters: [
        {
          name: "subscriptionType",
          label: "manage_stats.subscription_type",
          type: "select",
          options: getSubscriptionTypeOptions(),
        },
        {
          name: "agency",
          label: "manage_stats.agence",
          type: "select",
          options: getAgencyOptions(),
        },
        {
          name: "salePoint",
          label: "manage_stats.point_vente",
          type: "select",
          options: getSalePointOptions(),
        },
        {
          name: "salePeriod",
          label: "manage_stats.periode_vente",
          type: "select",
          options: getSalePeriodOptions(),
        },
        {
          name: "startDate",
          label: "manage_stats.start_date",
          type: "date",
        },
        {
          name: "endDate",
          label: "manage_stats.end_date",
          type: "date",
        },
      ],
      columns: [
        {
          title: t("manage_stats.point_vente"),
          dataIndex: `nom_${currentLang}`,
          key: "sale_point_name",
          render: (_: string, record: any) => record?.[`nom_${currentLang}`] || record?.nom_fr || '-',
        },
        {
          title: t("manage_stats.agence"),
          dataIndex: ["agency", `nom_${currentLang}`],
          key: "agency_name",
          render: (_: string, record: any) => record?.agency?.[`nom_${currentLang}`] || record?.agency?.nom_fr || '-',
        },
        {
          title: t("manage_stats.gouvernorat"),
          dataIndex: ["governorate", `nom_${currentLang}`],
          key: "governorate_name",
          render: (_: string, record: any) => record?.governorate?.[`nom_${currentLang}`] || record?.governorate?.nom_fr || '-',
        },
        {
          title: t("manage_stats.nb_abonnees"),
          dataIndex: ["all_times", "subscriber_count"],
          key: "subscriber_count_total",
          render: (_: string, record: any) => record?.all_times?.subscriber_count || 0,
        },
        {
          title: t("manage_stats.nb_transactions"),
          dataIndex: ["all_times", "transaction_count"],
          key: "transaction_count_total",
          render: (_: string, record: any) => record?.all_times?.transaction_count || 0,
        },
        {
          title: t("manage_stats.montant"),
          dataIndex: ["all_times", "total_amount"],
          key: "total_amount_all",
          render: (_: string, record: any) => `${record?.all_times?.total_amount?.toFixed(2) || 0} DT`
        },
      ],
    },
  };

  const loadDefaultData = (statisticType: string) => {
    if (statisticType === "1") {
      dispatch(getSubscribersByTrip({}));
    } else if (statisticType === "2") {
      dispatch(getSubscribersByEstablishment({}));
    } else if (statisticType === "3") {
      dispatch(getSubscribersByLineKmRange({}));
    } else if (statisticType === "4") {
      dispatch(getSubscribersByDiscount({}));
    } else if (statisticType === "5") {
      dispatch(getRevenuesBySalePeriod({}));
    } else if (statisticType === "6") {
      dispatch(getSubscribersByAgency({}));
    } else if (statisticType === "7") {
      dispatch(getSubscribersByLine({}));
    } else if (statisticType === "8") {
      dispatch(getRevenuesByClientGovernorate({}));
    } else if (statisticType === "9") {
      dispatch(getRevenuesByPaymentMethod({}));
    } else if (statisticType === "10") {
      dispatch(getSubscriptionsBySubsType({}));
    } else if (statisticType === "11") {
      dispatch(getSubscribersBySalePoint({}));
    }
  };

  const handleStatisticChange = (value: string) => {
    setSelectedStatistic(value);
    form.resetFields();
    dispatch(clearSubscribersByTrip());
    dispatch(clearSubscribersByEstablishment());
    dispatch(clearSubscribersByLineKmRange());
    dispatch(clearSubscribersByDiscount());
    dispatch(clearRevenuesBySalePeriod());
    dispatch(clearSubscribersByAgency());
    dispatch(clearSubscribersByLine());
    dispatch(clearRevenuesByClientGovernorate());
    dispatch(clearRevenuesByPaymentMethod());
    dispatch(clearSubscriptionsBySubsType());
    dispatch(clearSubscribersBySalePoint());

    // Reset pagination
    setPageNumber(1);
    setTotal(0);

    if (value) {
      loadDefaultData(value);
      // Reload ProTable data
      setTimeout(() => {
        actionRef.current?.reload();
      }, 100);
    }
  };

  const handleSubmit = async (values: any) => {
    if (!selectedStatistic) return;

    try {
      if (selectedStatistic === "1") {
        const params = {
          subscriptionType: values.subscriptionType,
          startDate: values.startDate ? dayjs(values.startDate).format('YYYY-MM-DD') : undefined,
          endDate: values.endDate ? dayjs(values.endDate).format('YYYY-MM-DD') : undefined,
          salePeriod: values.salePeriod,
          journey: values.journey,
        };
        dispatch(getSubscribersByTrip(params));
      } else if (selectedStatistic === "2") {
        const params = {
          subscriptionType: values.subscriptionType,
          startDate: values.startDate ? dayjs(values.startDate).format('YYYY-MM-DD') : undefined,
          endDate: values.endDate ? dayjs(values.endDate).format('YYYY-MM-DD') : undefined,
          salePeriod: values.salePeriod,
          establishment: values.establishment,
        };
        dispatch(getSubscribersByEstablishment(params));
      } else if (selectedStatistic === "3") {
        const params = {
          subscriptionType: values.subscriptionType,
          startDate: values.startDate ? dayjs(values.startDate).format('YYYY-MM-DD') : undefined,
          endDate: values.endDate ? dayjs(values.endDate).format('YYYY-MM-DD') : undefined,
          salePeriod: values.salePeriod,
          lineId: values.lineId,
        };
        dispatch(getSubscribersByLineKmRange(params));
      } else if (selectedStatistic === "4") {
        const params = {
          subscriptionType: values.subscriptionType,
          startDate: values.startDate ? dayjs(values.startDate).format('YYYY-MM-DD') : undefined,
          endDate: values.endDate ? dayjs(values.endDate).format('YYYY-MM-DD') : undefined,
        };
        dispatch(getSubscribersByDiscount(params));
      } else if (selectedStatistic === "5") {
        const params = {
          subscriptionType: values.subscriptionType,
          startDate: values.startDate ? dayjs(values.startDate).format('YYYY-MM-DD') : undefined,
          endDate: values.endDate ? dayjs(values.endDate).format('YYYY-MM-DD') : undefined,
        };
        dispatch(getRevenuesBySalePeriod(params));
      } else if (selectedStatistic === "6") {
        const params = {
          subscriptionType: values.subscriptionType,
          startDate: values.startDate ? dayjs(values.startDate).format('YYYY-MM-DD') : undefined,
          endDate: values.endDate ? dayjs(values.endDate).format('YYYY-MM-DD') : undefined,
        };
        dispatch(getSubscribersByAgency(params));
      } else if (selectedStatistic === "7") {
        const params = {
          subscriptionType: values.subscriptionType,
          startDate: values.startDate ? dayjs(values.startDate).format('YYYY-MM-DD') : undefined,
          endDate: values.endDate ? dayjs(values.endDate).format('YYYY-MM-DD') : undefined,
          salePeriod: values.salePeriod,
          lineId: values.lineId,
        };
        dispatch(getSubscribersByLine(params));
      } else if (selectedStatistic === "8") {
        const params = {
          subscriptionType: values.subscriptionType,
          startDate: values.startDate ? dayjs(values.startDate).format('YYYY-MM-DD') : undefined,
          endDate: values.endDate ? dayjs(values.endDate).format('YYYY-MM-DD') : undefined,
        };
        dispatch(getRevenuesByClientGovernorate(params));
      } else if (selectedStatistic === "9") {
        const params = {
          subscriptionType: values.subscriptionType,
          startDate: values.startDate ? dayjs(values.startDate).format('YYYY-MM-DD') : undefined,
          endDate: values.endDate ? dayjs(values.endDate).format('YYYY-MM-DD') : undefined,
        };
        dispatch(getRevenuesByPaymentMethod(params));
      } else if (selectedStatistic === "10") {
        const params = {
          startDate: values.startDate ? dayjs(values.startDate).format('YYYY-MM-DD') : undefined,
          endDate: values.endDate ? dayjs(values.endDate).format('YYYY-MM-DD') : undefined,
          salePeriod: values.salePeriod,
          salePoint: values.salePoint,
        };
        dispatch(getSubscriptionsBySubsType(params));
      } else if (selectedStatistic === "11") {
        const params = {
          subscriptionType: values.subscriptionType,
          startDate: values.startDate ? dayjs(values.startDate).format('YYYY-MM-DD') : undefined,
          endDate: values.endDate ? dayjs(values.endDate).format('YYYY-MM-DD') : undefined,
          salePeriod: values.salePeriod,
          salePoint: values.salePoint,
          agency: values.agency,
        };
        dispatch(getSubscribersBySalePoint(params));
      }

      // Reload ProTable data after filtering
      setTimeout(() => {
        actionRef.current?.reload();
      }, 100);
    } catch (error) {
      console.error("Error fetching data:", error);
    }
  };


  const flattenLineKmData = () => {
    if (!subscribersByLineKmRange?.data?.length) return [];

    // La nouvelle structure API retourne directement les tranches km
    return subscribersByLineKmRange.data.map((range: any) => ({
      kilometric_range: range.kilometric_range,
      km_range_start: range.km_range_start,
      km_range_end: range.km_range_end,
      all_times: {
        subscriber_count: range.all_times?.subscriber_count || 0,
        transaction_count: range.all_times?.transaction_count || 0,
        total_amount: range.all_times?.total_amount || 0,
        average_amount_per_subscriber: range.all_times?.average_amount_per_subscriber || 0,
      },
      last_month: {
        subscriber_count: range.last_month?.subscriber_count || 0,
        transaction_count: range.last_month?.transaction_count || 0,
        total_amount: range.last_month?.total_amount || 0,
        average_amount_per_subscriber: range.last_month?.average_amount_per_subscriber || 0,
      }
    }));
  };

  const calculateLineKmSummaryStats = () => {
    if (!subscribersByLineKmRange?.summary) {
      return {
        totalSubscribers: 0,
        totalTransactions: 0,
        totalAmount: 0,
        totalItems: 0,
        averagePerItem: 0
      };
    }

    const summary = subscribersByLineKmRange.summary.all_times;
    return {
      totalSubscribers: summary.grand_total_subscribers || 0,
      totalTransactions: summary.grand_total_transactions || 0,
      totalAmount: summary.grand_total_amount || 0,
      totalItems: summary.total_ranges || 0,
      averagePerItem: summary.average_amount_per_range || 0
    };
  };

  const prepareLineKmChartData = () => {
    if (!subscribersByLineKmRange?.data?.length) return [];

    return subscribersByLineKmRange.data.map((item: any) => ({
      nom: item.kilometric_range || `${item.km_range_start} - ${item.km_range_end} km`,
      abonnes: item.last_month?.subscriber_count || 0,
      transactions: item.last_month?.transaction_count || 0,
      montant: item.last_month?.total_amount || 0,
    }));
  };

  const calculateDiscountSummaryStats = () => {
    if (!subscribersByDiscount?.summary) {
      return {
        totalSubscribers: 0,
        totalTransactions: 0,
        totalAmount: 0,
        totalItems: 0,
        averagePerItem: 0
      };
    }

    const summary = subscribersByDiscount.summary.all_times;
    return {
      totalSubscribers: summary.total_abonnements || 0,
      totalTransactions: summary.total_transactions || 0,
      totalAmount: summary.total_montant || 0,
      totalItems: summary.total_discounts || 0,
      averagePerItem: summary.total_discounts > 0 ? (summary.total_montant || 0) / summary.total_discounts : 0
    };
  };

  const prepareDiscountChartData = () => {
    if (!subscribersByDiscount?.data?.length) return [];

    return subscribersByDiscount.data.map((item: any) => ({
      nom: `${item[`nom_${currentLang}`] || item.nom_fr || 'Remise'} (${item.percentage}%)`,
      abonnes: item.last_month?.nombre_abonnements || 0,
      transactions: item.last_month?.nombre_transactions || 0,
      montant: item.last_month?.montant_total || 0,
    }));
  };

  const calculateSalePeriodSummaryStats = () => {
    if (!revenuesBySalePeriod?.summary) {
      return {
        totalSubscribers: 0,
        totalTransactions: 0,
        totalAmount: 0,
        totalItems: 0,
        averagePerItem: 0
      };
    }

    const summary = revenuesBySalePeriod.summary.all_times;
    return {
      totalSubscribers: summary.total_abonnements || 0,
      totalTransactions: 0,
      totalAmount: summary.total_montant || 0,
      totalItems: summary.total_sale_periods || 0,
      averagePerItem: summary.total_sale_periods > 0 ? (summary.total_montant || 0) / summary.total_sale_periods : 0
    };
  };

  const calculateAgencySummaryStats = () => {
    if (!subscribersByAgency?.summary) {
      return {
        totalSubscribers: 0,
        totalTransactions: 0,
        totalAmount: 0,
        totalItems: 0,
        averagePerItem: 0
      };
    }

    const summary = subscribersByAgency.summary.all_times;
    return {
      totalSubscribers: summary.total_subscribers || 0,
      totalTransactions: summary.total_transactions || 0,
      totalAmount: summary.total_amount || 0,
      totalItems: summary.total_agencies || 0,
      averagePerItem: summary.total_agencies > 0 ? (summary.total_amount || 0) / summary.total_agencies : 0
    };
  };

  const calculateLineSummaryStats = () => {
    if (!subscribersByLine?.summary) {
      return {
        totalSubscribers: 0,
        totalTransactions: 0,
        totalAmount: 0,
        totalItems: 0,
        averagePerItem: 0
      };
    }

    const summary = subscribersByLine.summary.all_times;
    return {
      totalSubscribers: summary.total_subscribers || 0,
      totalTransactions: summary.total_transactions || 0,
      totalAmount: summary.total_amount || 0,
      totalItems: summary.total_lines || 0,
      averagePerItem: summary.total_lines > 0 ? (summary.total_amount || 0) / summary.total_lines : 0
    };
  };

  const prepareSalePeriodChartData = () => {
    if (!revenuesBySalePeriod?.data?.length) return [];

    return revenuesBySalePeriod.data.map((item: any) => ({
      nom: `${item[`sale_period_nom_${currentLang}`] || item.sale_period_nom_fr || 'Période'}`,
      abonnes: item.last_month?.nombre_abonnements || 0,
      transactions: item.last_month?.nombre_transactions || 0,
      montant: item.last_month?.montant_total || 0,
    }));
  };

  const prepareAgencyChartData = () => {
    if (!subscribersByAgency?.data?.length) return [];

    return subscribersByAgency.data.map((item: any) => ({
      nom: item[`nom_${currentLang}`] || item.agency_name || `Agence ${item.agency_id}`,
      abonnes: item.last_month?.subscriber_count || 0,
      transactions: item.last_month?.transaction_count || 0,
      montant: item.last_month?.total_amount || 0,
    }));
  };

  const prepareLineChartData = () => {
    if (!subscribersByLine?.data?.length) return [];

    return subscribersByLine.data.map((item: any) => ({
      nom: item[`nom_${currentLang}`] || item.line_name_fr || item.code || `Ligne ${item.line_id}`,
      abonnes: item.last_month?.subscriber_count || 0,
      transactions: item.last_month?.transaction_count || 0,
      montant: item.last_month?.total_amount || 0,
    }));
  };

  const calculateGovernorateRevenueSummaryStats = () => {
    if (!revenuesByClientGovernorate?.summary) {
      return {
        totalSubscribers: 0,
        totalTransactions: 0,
        totalAmount: 0,
        totalItems: 0,
        averagePerItem: 0
      };
    }

    const summary = revenuesByClientGovernorate.summary.all_times;
    return {
      totalSubscribers: summary.total_abonnements || 0,
      totalTransactions: summary.total_transactions || 0,
      totalAmount: summary.total_montant || 0,
      totalItems: summary.total_governorates || 0,
      averagePerItem: summary.total_governorates > 0 ? (summary.total_montant || 0) / summary.total_governorates : 0
    };
  };

  const prepareGovernorateRevenueChartData = () => {
    if (!revenuesByClientGovernorate?.data?.length) return [];

    return revenuesByClientGovernorate.data.map((item: any) => ({
      nom: item[`governorate_nom_${currentLang}`] || item.governorate_nom_fr || `Gouvernorat ${item.governorate_id}`,
      abonnes: item.last_month?.nombre_abonnements || 0,
      transactions: item.last_month?.nombre_transactions || 0,
      montant: item.last_month?.montant_total || 0,
    }));
  };

  const calculatePaymentMethodRevenueSummaryStats = () => {
    if (!revenuesByPaymentMethod?.summary) {
      return {
        totalSubscribers: 0,
        totalTransactions: 0,
        totalAmount: 0,
        totalItems: 0,
        averagePerItem: 0
      };
    }

    const summary = revenuesByPaymentMethod.summary.all_times;
    return {
      totalSubscribers: summary.total_abonnements || 0,
      totalTransactions: summary.total_transactions || 0,
      totalAmount: summary.total_montant || 0,
      totalItems: summary.total_payment_methods || 0,
      averagePerItem: summary.total_payment_methods > 0 ? (summary.total_montant || 0) / summary.total_payment_methods : 0
    };
  };

  const preparePaymentMethodRevenueChartData = () => {
    if (!revenuesByPaymentMethod?.data?.length) return [];

    return revenuesByPaymentMethod.data.map((item: any) => ({
      nom: item[`payment_method_nom_${currentLang}`] || item.payment_method_nom_fr || 'Méthode inconnue',
      abonnes: item.last_month?.nombre_abonnements || 0,
      transactions: item.last_month?.nombre_transactions || 0,
      montant: item.last_month?.montant_total || 0,
    }));
  };

  const calculateSubscriptionsBySubsTypeSummaryStats = () => {
    if (!subscriptionsBySubsType?.summary) {
      return {
        totalSubscribers: 0,
        totalTransactions: 0,
        totalAmount: 0,
        totalItems: 0,
        averagePerItem: 0
      };
    }

    const summary = subscriptionsBySubsType.summary.all_times;
    return {
      totalSubscribers: summary.total_abonnements || 0,
      totalTransactions: 0,
      totalAmount: summary.total_montant || 0,
      totalItems: summary.total_subs_types || 0,
      averagePerItem: summary.total_subs_types > 0 ? (summary.total_montant || 0) / summary.total_subs_types : 0
    };
  };

  const prepareSubscriptionsBySubsTypeChartData = () => {
    if (!subscriptionsBySubsType?.data?.length) return [];

    return subscriptionsBySubsType.data.map((item: any) => ({
      nom: item.is_impersonal ? item.type_display_name : (item[`subs_type_nom_${currentLang}`] || item.subs_type_nom_fr || 'Type inconnu'),
      abonnes: item.last_month?.nombre_abonnements || 0,
      transactions: item.last_month?.nombre_transactions || 0,
      montant: item.last_month?.montant_total || 0,
    }));
  };

  const calculateSalePointSummaryStats = () => {
    if (!subscribersBySalePoint?.summary) {
      return {
        totalSubscribers: 0,
        totalTransactions: 0,
        totalAmount: 0,
        totalItems: 0,
        averagePerItem: 0
      };
    }

    const summary = subscribersBySalePoint.summary.all_times;
    return {
      totalSubscribers: summary.total_subscribers || 0,
      totalTransactions: summary.total_transactions || 0,
      totalAmount: summary.total_amount || 0,
      totalItems: summary.total_sale_points || 0,
      averagePerItem: summary.total_sale_points > 0 ? (summary.total_amount || 0) / summary.total_sale_points : 0
    };
  };

  const prepareSalePointChartData = () => {
    if (!subscribersBySalePoint?.data?.length) return [];

    return subscribersBySalePoint.data.map((item: any) => ({
      nom: item[`nom_${currentLang}`] || item.nom_fr || 'Point de vente',
      abonnes: item.last_month?.subscriber_count || 0,
      transactions: item.last_month?.transaction_count || 0,
      montant: item.last_month?.total_amount || 0,
    }));
  };

  const currentConfig = selectedStatistic
    ? STATISTICS_CONFIG[selectedStatistic]
    : null;
  const breadcrumbItems = [
    {
      title: (
        <Link to="/auth/dashboard">
          {t("auth_sidebar.dashboard")}
        </Link>
      ),
    },
    {
      title: t("manage_stats.title"),
    },
  ];

  const summaryStats = calculateSummaryStats();
  const chartData = prepareChartData();

  const getColumnConfigForTransactions = () => {
    if (!chartData || chartData.length === 0) {
      return {
        data: [{ type: t("common.loading"), transactions: 0 }],
        xField: 'type',
        yField: 'transactions',
        height: 300,
        color: '#BC0202',
        columnStyle: {
          radius: [6, 6, 0, 0],
          fillOpacity: 0.8,
        },
        maxColumnWidth: 30,
      };
    }

    const data = [...chartData]
      .sort((a: any, b: any) => a.nom.localeCompare(b.nom))
      .map((item: any, index: number) => {
        return {
          type: item.nom,
          transactions: item.transactions || 0,
          montant: item.montant,
          index: index
        };
      });

    return {
      data,
      xField: 'type',
      yField: 'transactions',
     label: {
      position: 'top',
      content: 'montant', // <- this works
      style: {
        fill: '#fff',
        fontSize: 12,
        fontWeight: 'bold',
      },
      },
      height: 300,
      color: '#BC0202',
      columnStyle: {
        radius: [6, 6, 0, 0],
        fillOpacity: 0.8,
      },
      columnWidthRatio: 0.6,
      maxColumnWidth: 30,
    };
  };

  const getColumnConfigForSubscribers = () => {
    if (!chartData || chartData.length === 0) {
      return {
        data: [{ type: t("common.loading"), abonnes: 0 }],
        xField: 'type',
        yField: 'abonnes',
        height: 300,
        color: '#BC0202',
        columnStyle: {
          radius: [6, 6, 0, 0],
          fillOpacity: 0.8,
        },
        maxColumnWidth: 30,
      };
    }
    const data = [...chartData]
      .sort((a: any, b: any) => a.nom.localeCompare(b.nom))
      .map((item: any, index: number) => {
        return {
          type: item.nom,
          abonnes: item.abonnes,
          index: index
        };
      });

    return {
      data,
      xField: 'type',
      yField: 'abonnes',
      height: 300,
      color: '#BC0202',
      columnStyle: {
        radius: [6, 6, 0, 0],
        fillOpacity: 0.8,
      },
      columnWidthRatio: 0.6,
      maxColumnWidth: 30,
    };
  };

  const columnConfigForTransactions = getColumnConfigForTransactions();
  const columnConfigForSubscribers = getColumnConfigForSubscribers();

  const getPieConfig = () => {
    if (!chartData || chartData.length === 0) {
      return {
        data: [{ type: t("common.loading"), value: 0 }],
        angleField: 'value',
        colorField: 'type',
        legend: true,
        state: {
          inactive: { opacity: 0.5 },
          active: { opacity: 1 },
        },
        radius: 0.8,
        height: 300,
        style: { stroke: '#fff', lineWidth: 2 },
      };
    }

    return {
      data: chartData,
      angleField: 'abonnes',
      colorField: 'nom',
      label: {
        text: (d: any) => `${d.nom} (${d.abonnes})`,
        position: 'spider',
      },
      interaction: {
        elementHighlight: true,
      },
      state: {
        inactive: { opacity: 0.5 },
        active: { opacity: 1 },
      },
      legend: true,
      radius: 0.8,
      height: 300,
      style: { stroke: '#fff', lineWidth: 2 },
      theme: {
        colors10: ['#BC0202', '#10B981', '#3B82F6', '#8B5CF6', '#F59E0B', '#EF4444', '#06B6D4', '#84CC16', '#F97316', '#EC4899'],
      },
    };
  };

  const pieConfig = getPieConfig();

  const getLineConfig = () => {
    const getFieldLabels = () => {
      if (selectedStatistic === "1") {
        return { xField: 'trajet', yField: 'abonnes' };
      } else if (selectedStatistic === "2") {
        return { xField: 'etablissement', yField: 'abonnes' };
      } else if (selectedStatistic === "3") {
        return { xField: 'ligne', yField: 'abonnes' };
      } else if (selectedStatistic === "4") {
        return { xField: 'remise', yField: 'abonnes' };
      } else if (selectedStatistic === "5") {
        return { xField: 'periode', yField: 'abonnes' };
      } else if (selectedStatistic === "6") {
        return { xField: 'agence', yField: 'abonnes' };
      } else if (selectedStatistic === "7") {
        return { xField: 'ligne', yField: 'abonnes' };
      } else if (selectedStatistic === "8") {
        return { xField: 'gouvernorat', yField: 'abonnes' };
      } else if (selectedStatistic === "9") {
        return { xField: 'methode_paiement', yField: 'abonnes' };
      } else if (selectedStatistic === "10") {
        return { xField: 'nom', yField: 'abonnes' };
      } else if (selectedStatistic === "11") {
        return { xField: 'nom', yField: 'abonnes' };
      }
      return { xField: 'item', yField: 'abonnes' };
    };

    const { xField, yField } = getFieldLabels();

    if (!chartData || chartData.length === 0) {
      return {
        data: [{ [xField]: t("common.loading"), [yField]: 0 }],
        xField,
        yField,
        height: 300,
        color: '#BC0202',
        point: {
          size: 5,
          shape: 'diamond',
          fill: '#BC0202',
        },
      };
    }

    const data = [...chartData]
      .sort((a: any, b: any) => a.nom.localeCompare(b.nom))
      .map((item: any, index: number) => {
        return {
          [xField]: item.nom,
          [yField]: item.abonnes,
          index: index
        };
      });

    return {
      data,
      xField,
      yField,
      height: 300,
      smooth: true,
      color: '#BC0202',
      point: {
        size: 5,
        shape: 'diamond',
        fill: '#BC0202',
      },
    };
  };

  const lineConfig = getLineConfig();

  return (
    <div className="min-h-screen">
      <div className="mx-auto">
        <Breadcrumb className="mb-8" items={breadcrumbItems} />

        <div className="mb-8">
          <div
            style={{
                backgroundImage: `url(${assets.bg})`,
                backgroundPosition: "center",
                backgroundSize: "cover",
                backgroundRepeat: "no-repeat",
            }}
            className="rounded border border-gray-200 p-8">
            <div className="text-center">
              <div className="inline-flex items-center justify-center w-16 h-16 bg-red-50 rounded-full mb-4">
                <BarChartOutlined className="text-2xl text-red-600" />
              </div>
              <Title level={2} className="mb-2 text-gray-900">
                {t("manage_stats.paragraph")}
              </Title>
              <p className="text-gray-600 mb-8 text-base max-w-2xl mx-auto">
                Sélectionnez le type de statistique que vous souhaitez analyser pour obtenir des données détaillées
              </p>
              <div className="max-w-md mx-auto">
                <Select
                  size="large"
                  className="w-full"
                  placeholder={t("manage_stats.placeholder")}
                  onChange={handleStatisticChange}
                  value={selectedStatistic}
                >
                  {Object.entries(STATISTICS_CONFIG).map(([id, config]) => (
                    <Option key={id} value={id}>
                      {t(config.label)}
                    </Option>
                  ))}
                </Select>
              </div>
            </div>
          </div>
        </div>

        {currentConfig && (
          <div className="rounded shadow-sm border border-gray-200 mb-8">
            <Collapse
              ghost
              expandIconPosition="end"
              className="rounded"
            >
              <Panel
                header={
                  <div className="flex items-center py-3">
                    <div className="w-10 h-10 bg-red-700 rounded flex items-center justify-center mr-4">
                      <FilterOutlined className="text-white text-lg" />
                    </div>
                    <div>
                      <span className="font-semibold text-gray-900 text-lg">
                        Filtres de recherche
                      </span>
                      <p className="text-sm text-gray-500 mt-1">
                        Affinez vos résultats avec les filtres disponibles
                      </p>
                    </div>
                  </div>
                }
                key="filters"
                className="border-0"
              >
                <div className="px-6 pb-6">
                  <Form form={form} onFinish={handleSubmit} layout="vertical">
                    <Row gutter={[20, 20]}>
                      {currentConfig.filters.map((filter: any) => (
                        <Col xs={24} sm={12} md={8} lg={6} key={filter.name}>
                          <Form.Item
                            name={filter.name}
                            label={
                              <span className="font-medium text-gray-700 text-sm">
                                {t(filter.label)}
                              </span>
                            }
                          >
                            {filter.type === "select" ? (
                              <Select
                                size="large"
                                options={filter.options}
                                placeholder={t(filter.label)}
                                allowClear
                                className="w-full"
                              />
                            ) : filter.type === "date" ? (
                              <DatePicker
                                size="large"
                                className="w-full"
                              />
                            ) : (
                              <Input
                                size="large"
                                placeholder={t(filter.label)}
                                className="w-full"
                              />
                            )}
                          </Form.Item>
                        </Col>
                      ))}
                    </Row>
                    <div className="flex gap-4 mt-8 pt-6 border-t border-gray-100">
                      <Button
                        type="primary"
                        htmlType="submit"
                        loading={loading}
                        size="large"
                        icon={<BarChartOutlined />}
                        className="bg-red-600 hover:bg-red-700 border-red-600 font-medium px-8"
                      >
                        {t("manage_stats.btn")}
                      </Button>
                      <Button
                        type="default"
                        onClick={() => {
                          form.resetFields();
                          if (selectedStatistic) {
                            loadDefaultData(selectedStatistic);
                          }
                        }}
                        size="large"
                        className="font-medium px-6"
                      >
                        {t("manage_stats.reset")}
                      </Button>
                    </div>
                  </Form>
                </div>
              </Panel>
            </Collapse>
          </div>
        )}

        <div className="mb-8">
          <Row gutter={[24, 24]}>
            {/* Carte Total Abonnés */}
            <Col xs={24} sm={12} lg={6}>
              <div className="bg-white rounded border border-gray-200 p-6 hover:border-red-300 transition-colors duration-200">
                <div className="flex items-center justify-between mb-4">
                  <div className="w-12 h-12 bg-red-50 rounded flex items-center justify-center">
                    <UserOutlined className="text-red-600 text-xl" />
                  </div>
                  <div className="text-right">
                    <div className="text-2xl font-bold text-gray-900">
                      {summaryStats.totalSubscribers.toLocaleString()}
                    </div>
                    <div className="text-sm text-gray-500">
                      {t("manage_stats.total_abonnees")}
                    </div>
                  </div>
                </div>
                <div className="text-xs text-gray-400 uppercase tracking-wide">
                  {t("manage_stats.abonnees_total", "Abonnés actifs")}
                </div>
              </div>
            </Col>


            {/* Carte Total Montant */}
            <Col xs={24} sm={12} lg={6}>
              <div className="bg-white rounded border border-gray-200 p-6 hover:border-green-300 transition-colors duration-200">
                <div className="flex items-center justify-between mb-4">
                  <div className="w-12 h-12 bg-green-50 rounded flex items-center justify-center">
                    <DollarOutlined className="text-green-600 text-xl" />
                  </div>
                  <div className="text-right">
                    <div className="text-2xl font-bold text-gray-900">
                      {summaryStats.totalAmount.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })} DT
                    </div>
                    <div className="text-sm text-gray-500">
                      {t("manage_stats.total_montant")}
                    </div>
                  </div>
                </div>
                <div className="text-xs text-gray-400 uppercase tracking-wide">
                  {t("manage_stats.revenu_total", "Revenus générés")}
                </div>
              </div>
            </Col>

            <Col xs={24} sm={12} lg={6}>
              <div className="bg-white rounded border border-gray-200 p-6 hover:border-blue-300 transition-colors duration-200">
                <div className="flex items-center justify-between mb-4">
                  <div className="w-12 h-12 bg-blue-50 rounded flex items-center justify-center">
                    <DatabaseOutlined className="text-blue-600 text-xl" />
                  </div>
                  <div className="text-right">
                    <div className="text-2xl font-bold text-gray-900">
                      {summaryStats.totalItems}
                    </div>
                    <div className="text-sm text-gray-500">
                      {selectedStatistic === "1" ? t("manage_stats.total_trajets") :
                       selectedStatistic === "2" ? t("manage_stats.total_etablissements") :
                       selectedStatistic === "3" ? t("manage_stats.total_lignes") :
                       selectedStatistic === "4" ? t("manage_stats.total_remises") :
                       selectedStatistic === "5" ? t("manage_stats.total_periodes") :
                       selectedStatistic === "6" ? t("manage_stats.total_agences") :
                       selectedStatistic === "7" ? t("manage_stats.total_lignes") :
                       selectedStatistic === "8" ? t("manage_stats.total_gouvernorats") :
                       selectedStatistic === "9" ? t("manage_stats.total_methodes_paiement") :
                       selectedStatistic === "10" ? t("manage_stats.total_types_abn") :
                       selectedStatistic === "11" ? t("manage_stats.total_points_vente") :
                       t("manage_stats.total_lignes")}
                    </div>
                  </div>
                </div>
                <div className="text-xs text-gray-400 uppercase tracking-wide">
                  {selectedStatistic === "1" ? "Trajets disponibles" :
                   selectedStatistic === "2" ? "Établissements" :
                   selectedStatistic === "3" ? "Lignes actives" :
                   selectedStatistic === "4" ? "Remises disponibles" :
                   selectedStatistic === "5" ? "Périodes de vente" :
                   selectedStatistic === "6" ? "Agences" :
                   selectedStatistic === "7" ? "Lignes actives" :
                   selectedStatistic === "8" ? "Gouvernorats" :
                   selectedStatistic === "9" ? "Méthodes de paiement" :
                   selectedStatistic === "10" ? "Types d'abonnement" :
                   selectedStatistic === "11" ? "Points de vente" :
                   "Lignes actives"}
                </div>
              </div>
            </Col>

            <Col xs={24} sm={12} lg={6}>
              <div className="bg-white rounded border border-gray-200 p-6 hover:border-purple-300 transition-colors duration-200">
                <div className="flex items-center justify-between mb-4">
                  <div className="w-12 h-12 bg-purple-50 rounded flex items-center justify-center">
                    <TrophyOutlined className="text-purple-600 text-xl" />
                  </div>
                  <div className="text-right">
                    <div className="text-2xl font-bold text-gray-900">
                      {summaryStats.averagePerItem.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })} DT
                    </div>
                    <div className="text-sm text-gray-500">
                      {selectedStatistic === "1" ? t("manage_stats.moyenne_trajet") :
                       selectedStatistic === "2" ? t("manage_stats.moyenne_etablissement") :
                       selectedStatistic === "3" ? t("manage_stats.moyenne_ligne") :
                       selectedStatistic === "4" ? t("manage_stats.moyenne_remise") :
                       selectedStatistic === "5" ? t("manage_stats.moyenne_periode") :
                       selectedStatistic === "6" ? t("manage_stats.moyenne_agence") :
                       selectedStatistic === "7" ? t("manage_stats.moyenne_ligne") :
                       selectedStatistic === "8" ? t("manage_stats.moyenne_gouvernorat") :
                       selectedStatistic === "9" ? t("manage_stats.moyenne_methode_paiement") :
                       selectedStatistic === "10" ? t("manage_stats.moyenne_type_abn") :
                       selectedStatistic === "11" ? t("manage_stats.moyenne_point_vente") :
                       t("manage_stats.moyenne_ligne")}
                    </div>
                  </div>
                </div>
                <div className="text-xs text-gray-400 uppercase tracking-wide">
                    {t("manage_stats.revenu_par_item", "Moyenne par élément")}
                </div>
              </div>
            </Col>
          </Row>
        </div>

        <div className="space-y-8">
          {/* Graphiques */}
          {!loading && ((selectedStatistic === "1" && subscribersByTrip?.data?.length > 0) ||
                        (selectedStatistic === "2" && subscribersByEstablishment?.data?.length > 0) ||
                        (selectedStatistic === "3" && subscribersByLineKmRange?.data?.length > 0) ||
                        (selectedStatistic === "4" && subscribersByDiscount?.data?.length > 0) ||
                        (selectedStatistic === "5" && revenuesBySalePeriod?.data?.length > 0) ||
                        (selectedStatistic === "6" && subscribersByAgency?.data?.length > 0) ||
                        (selectedStatistic === "7" && subscribersByLine?.data?.length > 0) ||
                        (selectedStatistic === "8" && revenuesByClientGovernorate?.data?.length > 0) ||
                        (selectedStatistic === "9" && revenuesByPaymentMethod?.data?.length > 0) ||
                        (selectedStatistic === "10" && subscriptionsBySubsType?.data?.length > 0) ||
                        (selectedStatistic === "11" && subscribersBySalePoint?.data?.length > 0)) && (
            <Row gutter={[24, 24]}>
              <Col xs={24} lg={12}>
                <div className="bg-white rounded border border-gray-200">
                  <div className="px-6 py-5 border-b border-gray-100">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <div className="w-10 h-10 bg-red-50 rounded flex items-center justify-center mr-4">
                          <BarChartOutlined className="text-red-600 text-lg" />
                        </div>
                        <div>
                          <h3 className="text-gray-900 font-semibold text-lg mb-1">
                             {t("manage_stats.nb_abonnees")}
                          </h3>
                          <p className="text-gray-500 text-sm">
                            {selectedStatistic === "1" ? t("manage_stats.trajet") :
                             selectedStatistic === "2" ? t("manage_stats.etab") :
                             selectedStatistic === "3" ? t("manage_stats.ligne_tranch_km") :
                             selectedStatistic === "4" ? t("manage_stats.remise") :
                             selectedStatistic === "5" ? t("manage_stats.periode_vente") :
                             selectedStatistic === "6" ? t("manage_stats.agence") :
                             selectedStatistic === "7" ? t("manage_stats.ligne") :
                             selectedStatistic === "8" ? t("manage_stats.gouvernorat") :
                             selectedStatistic === "9" ? t("manage_stats.methode_paiement") :
                             selectedStatistic === "10" ? t("manage_stats.type_abn") :
                             selectedStatistic === "11" ? t("manage_stats.point_vente") :
                             t("manage_stats.ligne")} - {getLastMonthName()}
                          </p>
                        </div>
                      </div>
                      <Select
                        value={chartType}
                        onChange={setChartType}
                        size="middle"
                        className="min-w-[140px]"
                      >
                        <Option value="column">
                          <div className="flex items-center">
                            <BarChartOutlined className="mr-2 text-gray-600" />
                            <span>{t("manage_stats.column", "Colonnes")}</span>
                          </div>
                        </Option>
                        <Option value="pie">
                          <div className="flex items-center">
                            <PieChartOutlined className="mr-2 text-gray-600" />
                            <span>{t("manage_stats.pie", "Secteurs")}</span>
                          </div>
                        </Option>
                        <Option value="line">
                          <div className="flex items-center">
                            <LineChartOutlined className="mr-2 text-gray-600" />
                            <span>{t("manage_stats.line", "Lignes")}</span>
                          </div>
                        </Option>
                      </Select>
                    </div>
                  </div>
                  <div className="p-6">
                    <div style={{ height: 320 }}>
                      {chartType === 'column' && <Column {...columnConfigForSubscribers} />}
                      {chartType === 'pie' && <Pie {...pieConfig} />}
                      {chartType === 'line' && <Line {...lineConfig} />}
                    </div>
                  </div>
                </div>
              </Col>

              <Col xs={24} lg={12}>
                <div className="bg-white rounded border border-gray-200">
                  <div className="px-6 py-5 border-b border-gray-100">
                    <div className="flex items-center">
                      <div className="w-10 h-10 bg-green-50 rounded flex items-center justify-center mr-4">
                        <BarChartOutlined className="text-green-600 text-lg" />
                      </div>
                      <div>
                        <h3 className="text-gray-900 font-semibold text-lg mb-1">
                          {t("manage_stats.nb_transactions")}
                        </h3>
                        <p className="text-gray-500 text-sm">
                          {selectedStatistic === "1" ? t("manage_stats.trajet") :
                           selectedStatistic === "2" ? t("manage_stats.etab") :
                           selectedStatistic === "3" ? t("manage_stats.ligne_tranch_km") :
                           selectedStatistic === "4" ? t("manage_stats.remise") :
                           selectedStatistic === "5" ? t("manage_stats.periode_vente") :
                           selectedStatistic === "6" ? t("manage_stats.agence") :
                           selectedStatistic === "7" ? t("manage_stats.ligne") :
                           selectedStatistic === "8" ? t("manage_stats.gouvernorat") :
                           selectedStatistic === "9" ? t("manage_stats.methode_paiement") :
                           selectedStatistic === "10" ? t("manage_stats.type_abn") :
                           selectedStatistic === "11" ? t("manage_stats.point_vente") :
                           t("manage_stats.ligne")} - {getLastMonthName()}
                        </p>
                      </div>
                    </div>
                  </div>
                  <div className="p-6">
                    <div style={{ height: 320 }}>
                      <Column {...columnConfigForTransactions} />
                    </div>
                  </div>
                </div>
              </Col>
            </Row>
          )}

          {/* Tableau des données */}
          <div className="bg-white px-4 modernTable rounded border border-gray-200">
            <div className="px-6 py-5 border-b border-gray-100">
              <div className="flex items-center">
                <div className="w-10 h-10 bg-blue-50 rounded flex items-center justify-center mr-4">
                  <SettingOutlined className="text-blue-600 text-lg" />
                </div>
                <div>
                  <h3 className="text-gray-900 font-semibold text-lg mb-1">
                    {t("manage_stats.donnees_detaillees")}
                  </h3>
                  <p className="text-gray-500 text-sm">
                    {t("manage_stats.donnees_detaillees_desc", "Tableau complet des données statistiques - Toutes les données")}
                  </p>
                </div>
              </div>
            </div>
            <div>
              <div className="overflow-hidden">
                <ProTable
                  columns={currentConfig?.columns}
                  actionRef={actionRef}
                  cardBordered={false}
                  rowKey={selectedStatistic === "1" ? "trip_id" :
                          selectedStatistic === "2" ? "establishment_id" :
                          selectedStatistic === "3" ? (record: any) => `${record.line_id}-${record.km_range_start}` :
                          selectedStatistic === "4" ? "discount_id" :
                          selectedStatistic === "5" ? "sale_period_id" :
                          selectedStatistic === "6" ? "agency_id" :
                          selectedStatistic === "7" ? "line_id" :
                          selectedStatistic === "8" ? "governorate_id" :
                          selectedStatistic === "9" ? "payment_method_id" :
                          selectedStatistic === "10" ? (record: any) => `${record.subs_type_id}-${record.is_impersonal ? record.is_moral_type : 'regular'}` :
                          selectedStatistic === "11" ? "sale_point_id" :
                          "line_id"}
                  request={async (params: any, sort: any, filter: any) => {
                    setTableLoading(true);
                    const dataFilter: any = await handleGetStatisticsData(params, sort, filter);
                    setTableLoading(false);
                    return {
                      data: dataFilter,
                      success: true,
                    };
                  }}
                  pagination={{
                    pageSize: pageSize,
                    total: total,
                    showSizeChanger: true,
                    onChange: (page) => setPageNumber(page),
                    onShowSizeChange: (_, size) => setPageSize(size),
                    showTotal: (total, range) =>
                      `${range[0]}-${range[1]} sur ${total} éléments`,
                  }}
                  scroll={{ x: 800 }}
                  loading={tableLoading}
                  search={false}
                  toolBarRender={false}
                  size="middle"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ManageStats;
